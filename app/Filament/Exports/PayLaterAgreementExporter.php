<?php

namespace App\Filament\Exports;

use App\Models\PayLaterAgreement;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class PayLaterAgreementExporter extends Exporter
{
    protected static ?string $model = PayLaterAgreement::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('ID'),
            ExportColumn::make('created_at')
                ->label('Date Created'),
            ExportColumn::make('account.short_name')
                ->label('Account'),
            ExportColumn::make('payable.customer.full_name')
                ->label('Customer Name'),
            ExportColumn::make('payable.customer.email')
                ->label('Customer Email'),
            ExportColumn::make('payable.customer.phone')
                ->label('Customer Phone'),
            ExportColumn::make('payable_type')
                ->label('Type')
                ->formatStateUsing(fn (string $state) => strtoupper(str_replace('-', ' ', $state))),
            ExportColumn::make('is_approved')
                ->label('Pre-approved')
                ->formatStateUsing(fn (bool $state) => $state ? 'Yes' : 'No'),
            ExportColumn::make('status')
                ->label('Status'),
            ExportColumn::make('payLaterPlan.name')
                ->label('Plan'),
            ExportColumn::make('dealer_payment')
                ->label('Dealer Payment'),
            ExportColumn::make('loan_amount')
                ->label('Loan Amount'),
            ExportColumn::make('commission_rate')
                ->label('Dealer Percentage'),
            ExportColumn::make('commission_rate_margin')
                ->label('Margin'),
            ExportColumn::make('description')
                ->label('Description'),
            ExportColumn::make('provider_reference')
                ->label('Provider Reference'),
            ExportColumn::make('token')
                ->label('Token'),
            ExportColumn::make('url')
                ->label('URL'),
            ExportColumn::make('payable.vrm')
                ->label('VRM'),
            ExportColumn::make('payable.vehicle_make')
                ->label('Vehicle Make'),
            ExportColumn::make('payable.vehicle_model')
                ->label('Vehicle Model'),
            ExportColumn::make('payable.vehicle_derivative')
                ->label('Vehicle Derivative'),
            ExportColumn::make('payable.vehicle_colour')
                ->label('Vehicle Colour'),
            ExportColumn::make('payable.fuel_type')
                ->label('Fuel Type'),
            ExportColumn::make('payable.transmission_type')
                ->label('Transmission Type'),
            ExportColumn::make('payable.engine_capacity')
                ->label('Engine Capacity'),
            ExportColumn::make('payable.registration_date')
                ->label('Registration Date'),
            ExportColumn::make('payable.vin')
                ->label('VIN'),
            ExportColumn::make('updated_at')
                ->label('Last Updated'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your pay later agreement export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
}
