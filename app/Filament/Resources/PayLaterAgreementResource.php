<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PayLaterAgreementResource\Pages;
use App\Models\PayLaterAgreement;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class PayLaterAgreementResource extends Resource
{
    protected static ?string $model = PayLaterAgreement::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with('payable.customer');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date created')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('account.short_name')
                    ->label('Account')
                    ->toggleable()
                    ->visible(fn () => Auth::user()->isViewingAllRecords())
                    ->sortable(),
                Tables\Columns\TextColumn::make('payable.customer.full_name')
                    ->searchable(query: fn (Builder $query, string $search) => $query
                        ->whereHas('payable', fn (Builder $q) => $q
                            ->whereHas('customer', fn (Builder $q) => $q->search($search))))
                    ->sortable(),
                Tables\Columns\TextColumn::make('payable_type')
                    ->badge()
                    ->alignCenter()
                    ->color('success')
                    ->formatStateUsing(fn (string $state) => strtoupper(str_replace('-', ' ', $state))),
                Tables\Columns\IconColumn::make('is_approved')
                    ->label('Pre-approved')
                    ->alignCenter()
                    ->boolean(),
                Tables\Columns\TextColumn::make('payLaterPlan.name')
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                Tables\Columns\TextColumn::make('dealer_payment')
                    ->placeholder('-')
                    ->alignCenter()
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('loan_amount')
                    ->placeholder('-')
                    ->alignCenter()
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_rate')
                    ->label('Dealer Percentage')
                    ->tooltip('The percentage of the loan amount that the dealer pays to the financing provider.')
                    ->placeholder('-')
                    ->alignCenter()
                    ->numeric()
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_rate_margin')
                    ->label('Margin')
                    ->tooltip('The percentage of the loan amount the financing provider rebates to Protego.')
                    ->placeholder('-')
                    ->alignCenter()
                    ->numeric()
                    ->suffix('%')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('pay_later_plan')
                    ->relationship('payLaterPlan', 'name'),
            ])
            ->actions([
            ])
            ->bulkActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayLaterAgreements::route('/'),
            'view' => Pages\ViewPayLaterAgreement::route('/{record}'),
        ];
    }
}
