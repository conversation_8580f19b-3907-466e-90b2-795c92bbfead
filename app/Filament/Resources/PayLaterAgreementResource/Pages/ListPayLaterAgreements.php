<?php

namespace App\Filament\Resources\PayLaterAgreementResource\Pages;

use App\Filament\Exports\PayLaterAgreementExporter;
use App\Filament\Resources\PayLaterAgreementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPayLaterAgreements extends ListRecords
{
    protected static string $resource = PayLaterAgreementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ExportAction::make()
                ->exporter(PayLaterAgreementExporter::class),
        ];
    }
}
