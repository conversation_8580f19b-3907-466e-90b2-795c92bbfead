<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PayLaterServiceResource\Pages;
use App\Models\PayLaterAgreement;
use App\Models\PayLaterService;
use Filament\Infolists\Components\Actions\Action;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class PayLaterServiceResource extends Resource
{
    protected static ?string $model = PayLaterService::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('customer.full_name')
                    ->searchable(['first_name', 'last_name', 'email'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('vrm')
                    ->label('VRM')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\IconColumn::make('payLaterAgreement.is_approved')
                    ->label('Approved')
                    ->boolean()
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('payLaterAgreement.status')
                    ->label('Status')
                    ->badge()
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('payLaterAgreement.loan_amount')
                    ->label('Loan Amount')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payLaterAgreement.dealer_payment')
                    ->label('Deposit Amount')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('invoice_amount')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('dealer_payment')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema(fn (PayLaterService $record) => [
                CustomerResource::getCustomerDetailsSection($record->customer)
                    ->relationship('customer')
                    ->collapsed(false)
                    ->persistCollapsed(),
                static::getVehicleDetailsSection()
                    ->collapsed(false)
                    ->persistCollapsed(),
                static::getPayLaterAgreementSection()
                    ->collapsed(false)
                    ->persistCollapsed(),
                static::getServiceDetailsSection()
                    ->collapsed(false)
                    ->persistCollapsed(),
            ]);
    }

    public static function getVehicleDetailsSection(): Section
    {
        return Section::make('Vehicle Information')
            ->columns(3)
            ->schema([
                TextEntry::make('vrm')
                    ->label('Registration')
                    ->icon('heroicon-o-identification'),
                TextEntry::make('vehicle_make')
                    ->label('Make'),
                TextEntry::make('vehicle_model')
                    ->label('Model'),
                TextEntry::make('vehicle_derivative')
                    ->label('Derivative')
                    ->placeholder('N/A'),
                TextEntry::make('vehicle_colour')
                    ->label('Colour'),
                TextEntry::make('fuel_type')
                    ->label('Fuel Type'),
                TextEntry::make('transmission_type')
                    ->label('Transmission'),
                TextEntry::make('engine_capacity')
                    ->label('Engine Size (cc)')
                    ->placeholder('N/A'),
                TextEntry::make('registration_date')
                    ->label('Registration Date')
                    ->date(),
                TextEntry::make('vin')
                    ->label('VIN')
                    ->placeholder('N/A')
                    ->columnSpanFull(),
            ]);
    }

    public static function getPayLaterAgreementSection(): Section
    {
        return PayLaterAgreementResource::getAgreementDetailsSection()
            ->relationship('payLaterAgreement')
            ->headerActions([
                Action::make('view_pay_later_agreement')
                    ->url(fn (PayLaterService $record) => PayLaterAgreementResource::getUrl('view', [$record->payLaterAgreement])),
            ])
            ->collapsed(false)
            ->persistCollapsed();
    }

    public static function getServiceDetailsSection(): Section
    {
        return Section::make('Service Details')
            ->columns(3)
            ->schema([
                TextEntry::make('invoice_amount')
                    ->label('Invoice Amount')
                    ->money('GBP')
                    ->placeholder('N/A'),
                TextEntry::make('created_at')
                    ->label('Created')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayLaterServices::route('/'),
            'create' => Pages\CreatePayLaterService::route('/create'),
            'view' => Pages\ViewPayLaterService::route('/{record}'),
        ];
    }
}
