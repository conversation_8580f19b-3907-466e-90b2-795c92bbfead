<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Actions\PayLater\BeginPayLaterApplication;
use App\Actions\PayLater\PreApproveSaleForPayLater;
use App\Events\SaleConfirmed;
use App\Filament\Resources\SaleResource;
use App\Models\AccountBreakdownProduct;
use App\Models\AccountPayLaterPlan;
use App\Models\AccountServicePlanProduct;
use App\Models\AccountWarrantyBreakdownProductBundle;
use App\Models\AccountWarrantyProduct;
use App\Models\BreakdownProduct;
use App\Models\PayLaterAgreement;
use App\Models\PayLaterPlan;
use App\Models\ProductGroup;
use App\Models\Sale;
use App\Models\SaleProduct;
use App\Models\ServicePlanProduct;
use App\Models\ServiceType;
use App\Services\Payments\PayLater\PayLaterPaymentProcessor;
use App\Services\Surcharge\ManufacturerSurchargeRepository;
use App\Services\Tax\VatCalculator;
use Closure;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Actions\StaticAction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\MaxWidth;
use Filament\Support\Exceptions\Halt;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Gate;

class AddSaleProducts extends EditRecord
{
    protected static string $resource = SaleResource::class;

    protected static ?string $title = 'Product Selection';

    protected static ?string $breadcrumb = 'Product Selection';

    protected function authorizeAccess(): void
    {
        // It is important that the user is checked on the ability to create a sale.
        // As class extends EditRecord, the default check is the ability to update a sale.
        Gate::authorize('create', Sale::class);
    }

    public function getSubheading(): ?string
    {
        return "{$this->getRecord()->vrm} {$this->getRecord()->vehicle_details} - {$this->getRecord()->customer->full_name}";
    }

    public function mount(int|string $record): void
    {
        parent::mount($record);
        if ($this->getSale()->confirmed_at) {
            $this->redirect(ViewSale::getUrl(['record' => $this->getRecord()]));
        }
    }

    public function getSubNavigation(): array
    {
        return [];
    }

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make()->url(EditSale::getUrl(['record' => $this->getRecord()])),
            DeleteAction::make(),
        ];
    }

    public function cancelPreApproveModal()
    {
        $this->data['is_pay_later_agreement'] = false;

        return $this;
    }

    public function preApproveModal(): Action
    {
        return Action::make('preApproveModal')
            ->requiresConfirmation()
            ->label('Pre-Approve for Pay Later')
            ->modalDescription('Do you want to pre-approve this sale for Pay Later?')
            ->modalCloseButton(false)
            ->modalSubmitAction(fn (StaticAction $action) => $action->label('Yes'))
            ->modalCancelAction(fn (StaticAction $action) => $action
                ->label('No')
                ->action('cancelPreApproveModal'))
            ->action(function (Sale $sale, Action $action, PreApproveSaleForPayLater $preApproveSaleForPayLater) {
                $preApproveSaleForPayLater->execute($sale);
                if ($sale->refresh()->payLaterAgreement->is_approved) {
                    $action->success();
                } else {
                    $this->cancelPreApproveModal();
                    $action->failure();
                }
            })
            ->successNotificationTitle('This customer has been pre-approved for Pay Later.')
            ->failureNotificationTitle('This customer has not been pre-approved for Pay Later.');
    }

    public function form(Form $form): Form
    {
        $warrantyProducts = $this->getWarrantyProducts();
        $breakdownPlanProducts = $this->getBreakdownPlanProducts();
        $servicePlanProducts = $this->getServicePlanProducts();

        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->afterStateHydrated($this->onSellingPriceUpdate(...))
                    ->afterStateUpdated($this->onSellingPriceUpdate(...))
                    ->maxWidth('2xl')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->hidden($warrantyProducts->isEmpty())
                            ->relationship(
                                name: 'warranty',
                                condition: fn (?array $state): bool => filled($state['account_warranty_product_id']),
                            )
                            ->mutateRelationshipDataBeforeCreateUsing($this->mutateWarrantyData())
                            ->mutateRelationshipDataBeforeSaveUsing($this->mutateWarrantyData())
                            ->schema([
                                Forms\Components\Select::make('account_warranty_product_id')
                                    ->live()
                                    ->label('Warranty')
                                    ->placeholder('No warranty')
                                    ->afterStateUpdated($this->onWarrantyProductUpdate())
                                    ->options($warrantyProducts),
                                Forms\Components\TextInput::make('selling_price')
                                    ->lazy()
                                    ->prefix('£')
                                    ->visible(fn (Forms\Get $get): bool => (bool) $get('account_warranty_product_id'))
                                    ->numeric()
                                    ->required(),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->hidden($breakdownPlanProducts->isEmpty())
                            ->relationship(
                                name: 'breakdownPlan',
                                condition: fn (): bool => $this->hasBreakdownProduct(),
                            )
                            ->afterStateHydrated($this->onWarrantyProductUpdate())
                            ->mutateRelationshipDataBeforeCreateUsing($this->mutateBreakdownPlanData())
                            ->mutateRelationshipDataBeforeSaveUsing($this->mutateBreakdownPlanData())
                            ->schema([
                                Forms\Components\Placeholder::make('is_bundled')
                                    ->label('AA Breakdown Cover')
                                    ->visible(fn (Forms\Get $get): bool => (bool) $get('is_bundled'))
                                    ->content('Bundled with the warranty'),
                                Forms\Components\Select::make('account_breakdown_product_id')
                                    ->live()
                                    ->label('AA Breakdown Cover')
                                    ->placeholder('No breakdown cover')
                                    ->hidden(fn (Forms\Get $get): bool => (bool) $get('is_bundled'))
                                    ->dehydrated()
                                    ->helperText(fn (Forms\Get $get) => $get('is_bundled') ? 'Bundled with the warranty' : null)
                                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                        $accountBreakdownProductId = $get('account_breakdown_product_id');
                                        if ($accountBreakdownProductId) {
                                            $accountBreakdownProduct = AccountBreakdownProduct::where('account_id', $this->getSale()->account_id)
                                                ->find($accountBreakdownProductId);

                                            $set('data.accountBreakdownProduct', $accountBreakdownProduct, true);

                                            $set('selling_price', $accountBreakdownProduct->selling_price);
                                        } else {
                                            $set('data.accountBreakdownProduct', null, true);
                                            $set('selling_price', null);
                                        }

                                        $this->onSellingPriceUpdate($set);
                                    })
                                    ->options($breakdownPlanProducts),
                                Forms\Components\TextInput::make('selling_price')
                                    ->lazy()
                                    ->prefix('£')
                                    ->visible(fn (Forms\Get $get): bool => (bool) $get('account_breakdown_product_id'))
                                    ->numeric()
                                    ->required(),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->hidden($servicePlanProducts->isEmpty())
                            ->relationship(
                                name: 'servicePlan',
                                condition: fn (?array $state): bool => filled($state['account_service_plan_product_id']),
                            )
                            ->mutateRelationshipDataBeforeCreateUsing($this->mutateServicePlanData())
                            ->mutateRelationshipDataBeforeSaveUsing($this->mutateServicePlanData())
                            ->saveRelationshipsUsing(function (Sale $record, array $state) {
                                if (! filled($state['account_service_plan_product_id'])) {
                                    return;
                                }

                                $accountServicePlanProduct = AccountServicePlanProduct::where('account_id', $this->getSale()->account_id)
                                    ->find($state['account_service_plan_product_id']);

                                // Copy account service plan limits to service plan so
                                // that future changes to the plan do not affect existing sales
                                $record->servicePlan->serviceTypes()->sync(
                                    $accountServicePlanProduct
                                        ->serviceTypes
                                        ->mapWithKeys(fn (ServiceType $type) => [$type->id => ['limit' => $type->pivot->limit]])
                                );
                            })
                            ->schema([
                                Forms\Components\Select::make('account_service_plan_product_id')
                                    ->live()
                                    ->label('Service Plan')
                                    ->placeholder('No service plan')
                                    ->live()
                                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                        $accountServicePlanProductId = $get('account_service_plan_product_id');
                                        if ($accountServicePlanProductId) {
                                            $accountServicePlanProduct = AccountServicePlanProduct::where('account_id', $this->getSale()->account_id)
                                                ->find($accountServicePlanProductId);
                                            $set('selling_price', $accountServicePlanProduct->selling_price);
                                        } else {
                                            $set('selling_price', null);
                                        }

                                        $this->onSellingPriceUpdate($set);
                                    })
                                    ->options($servicePlanProducts),
                                Forms\Components\TextInput::make('selling_price')
                                    ->lazy()
                                    ->prefix('£')
                                    ->visible(fn (Forms\Get $get): bool => (bool) $get('account_service_plan_product_id'))
                                    ->numeric()
                                    ->required(),
                            ]),
                        ...$this->mapProductGroups(),
                        Forms\Components\TextInput::make('total')
                            ->prefix('£')
                            ->disabled()
                            ->numeric()
                            ->maxWidth(MaxWidth::ExtraSmall),
                        Forms\Components\ToggleButtons::make('is_pay_later_agreement')
                            ->label('Pay Later Agreement')
                            ->formatStateUsing(fn (Sale $record) => (bool) $record->payLaterAgreement?->is_approved)
                            ->disabled(fn (Sale $record) => $record->payLaterAgreement?->is_approved === false)
                            ->helperText(function (Sale $record) {
                                if ($record->payLaterAgreement?->is_approved === false) {
                                    return 'Pay later pre-approval failed.';
                                }

                                return null;
                            })
                            ->afterStateUpdated(function (bool $state, Forms\Set $set, $livewire, Sale $record) {
                                if ($state === true) {
                                    if ($record->payLaterAgreement()->doesntExist()) {
                                        $livewire->mountAction('preApproveModal');
                                    } else {
                                        $set('data.payLaterAgreement.loan_amount', $this->getTotalSellingPrice(), true);
                                        $this->onSellingPriceUpdate($set);
                                    }
                                }
                            })
                            ->visible($this->getSale()->account->hasPayLater())
                            ->live()
                            ->inline()
                            ->boolean(),
                        Forms\Components\Grid::make(1)
                            ->visible(fn (Forms\Get $get) => (bool) $get('is_pay_later_agreement'))
                            ->relationship('payLaterAgreement')
                            ->mutateRelationshipDataBeforeSaveUsing(function ($data) {
                                /** @var AccountPayLaterPlan $accountPayLaterPlan */
                                $payLaterPlan = $this->getSale()->account->payLaterPlans()->findOrFail($data['pay_later_plan_id']);

                                return [
                                    ...$data,
                                    ...$payLaterPlan->only(['commission_rate', 'commission_rate_margin']),
                                    'description' => $this->getSale()->description(),
                                ];
                            })
                            ->schema([
                                Forms\Components\Select::make('pay_later_plan_id')
                                    ->relationship('payLaterPlan', 'name', fn ($query) => $query->where('account_id', $this->getSale()->account_id)->active())
                                    ->preload()
                                    ->live()
                                    ->required(),
                                Forms\Components\TextInput::make('dealer_payment')
                                    ->disabled(fn (?PayLaterAgreement $record) => ! $record?->is_approved)
                                    ->lazy()
                                    ->prefix('£')
                                    ->numeric()
                                    ->minValue(function (Get $get) {
                                        $min = [0];
                                        if ($get('pay_later_plan_id')) {
                                            // calculate min deposit amount in case plan max is less than total selling price
                                            $min[] = $this->getTotalSellingPrice() - PayLaterPlan::find($get('pay_later_plan_id'))->max_amount;
                                        }

                                        // return the highest of the min amounts so that we don't go below zero
                                        return max($min);
                                    })
                                    ->maxValue(function (Get $get) {
                                        $max = [$this->getTotalSellingPrice()];
                                        if ($get('pay_later_plan_id')) {
                                            $max[] = $this->getTotalSellingPrice() - PayLaterPlan::find($get('pay_later_plan_id'))->min_amount;
                                        }

                                        return min($max);
                                    })

                                    ->maxWidth(MaxWidth::ExtraSmall),
                                Forms\Components\TextInput::make('loan_amount')
                                    ->disabled()
                                    ->dehydrated(true)
                                    ->prefix('£')
                                    ->helperText(function ($state, Get $get, PayLaterPaymentProcessor $payLaterPaymentProcessor) {
                                        $payLaterPlanId = $get('pay_later_plan_id');
                                        if (! $payLaterPlanId || $state == 0) {
                                            return null;
                                        }

                                        return $payLaterPaymentProcessor
                                            ->forAccount($this->getSale()->account)
                                            ->getPlanBreakdown(PayLaterPlan::find($payLaterPlanId), $state)->forHumans();
                                    })
                                    ->numeric()
                                    ->required()
                                    ->maxWidth(MaxWidth::ExtraSmall),
                            ]),
                        Forms\Components\Placeholder::make('confirmation')
                            ->content('Please confirm that you have added all of the products. After doing so, they cannot be amended.'),
                    ]),
            ]);
    }

    protected function onWarrantyProductUpdate(): Closure
    {
        return function (Forms\Get $get, Forms\Set $set, ManufacturerSurchargeRepository $manufacturerSurchargeRepository) {
            $accountWarrantyProductId = $get('data.warranty.account_warranty_product_id', true);
            if ($accountWarrantyProductId) {
                $accountWarrantyProduct = AccountWarrantyProduct::where('account_id', $this->getSale()->account_id)
                    ->with('bundledBreakdownProduct')
                    ->find($accountWarrantyProductId);

                $set('data.breakdownPlan.is_bundled', (bool) $accountWarrantyProduct->bundledBreakdownProduct, true);
                $set('data.accountWarrantyProduct', $accountWarrantyProduct, true);

                $set(
                    'selling_price',
                    number_format(
                        $manufacturerSurchargeRepository
                            ->lookupSurcharge($this->getSale())->addSellingPriceSurcharge($accountWarrantyProduct->selling_price),
                        decimals: 2,
                        thousands_separator: ''
                    )
                );
            } else {
                $set('data.breakdownPlan.is_bundled', false, true);
                $set('data.accountWarrantyProduct', null, true);
                $set('selling_price', null);
            }

            $this->onSellingPriceUpdate($set);
        };
    }

    protected function onSellingPriceUpdate(Forms\Set $set)
    {
        $set(
            path: 'data.total',
            state: number_format($this->getTotalSellingPrice(), decimals: 2, thousands_separator: ''),
            isAbsolute: true
        );
        $set(
            'data.payLaterAgreement.loan_amount',
            state: number_format($this->getLoanAmount(), decimals: 2, thousands_separator: ''),
            isAbsolute: true
        );
        $set(
            'data.payLaterAgreement.dealer_payment',
            state: number_format($this->getDepositAmount(), decimals: 2, thousands_separator: ''),
            isAbsolute: true
        );
    }

    protected function getTotalSellingPrice()
    {
        return array_sum([
            $this->data['warranty']['selling_price'] ?? 0,
            $this->data['breakdownPlan']['selling_price'] ?? 0,
            $this->data['servicePlan']['selling_price'] ?? 0,
            collect($this->data['productGroups'] ?? [])->pluck('selling_price')->sum(),
        ]);
    }

    protected function getDepositAmount()
    {
        return $this->data['is_pay_later_agreement'] ?? false
            ? $this->data['payLaterAgreement']['dealer_payment']
            : '0.00';
    }

    protected function getLoanAmount()
    {
        return $this->data['is_pay_later_agreement'] ?? false
            ? $this->getTotalSellingPrice() - $this->getDepositAmount()
            : '0.00';
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $productGroups = $this->getProductGroups();

        if ($productGroups->isNotEmpty()) {
            $this->getSale()
                ->products()
                ->whereNotIn('product_id', collect($data['productGroups'])->pluck('product_id')->filter())->delete();

            foreach ($productGroups as $productGroup) {
                $saleProductData = $data['productGroups'][$productGroup->id];
                if (! $saleProductData['product_id']) {
                    continue;
                }
                $product = $productGroup->products->find($saleProductData['product_id']);
                $this->getSale()->products()->updateOrCreate([
                    'product_id' => $saleProductData['product_id'],
                ], [
                    'name' => $product->name,
                    'cost_price' => $product->cost_price,
                    'dealer_commission' => $product->dealer_commission,
                    'selling_price' => $saleProductData['selling_price'],
                ]);
            }
        }

        return [
            'confirmed_at' => now(),
        ];
    }

    public function afterSave()
    {
        SaleConfirmed::dispatch($this->getSale());

        if ($this->getSale()?->refresh()->isPayLater()) {
            app(BeginPayLaterApplication::class)->execute($this->getSale()->payLaterAgreement, sendNotification: true);
        }
    }

    protected function getSaveFormAction(): Action
    {
        return parent::getSaveFormAction()
            ->label('Confirm Sale')
            ->disabled(function () {
                if ($this->data['is_pay_later_agreement'] && ! $this->getSale()->payLaterAgreement?->is_approved) {
                    return true;
                }

                return
                    ! $this->data['warranty']['account_warranty_product_id'] &&
                    ! $this->data['breakdownPlan']['account_breakdown_product_id'] &&
                    ! $this->data['servicePlan']['account_service_plan_product_id'] &&
                    collect($this->data['productGroups'] ?? [])->pluck('product_id')->filter()->isEmpty();
            });
    }

    protected function getWarrantyProducts()
    {
        return $this->getSale()
            ->getEligibleWarrantyProducts()
            ->groupBy('product_id')
            ->map->first()
            ->values()
            ->mapWithKeys(fn (AccountWarrantyProduct $accountProduct) => [$accountProduct->getKey() => $accountProduct->getLabel()]);
    }

    protected function getBreakdownPlanProducts()
    {
        $sale = $this->getSale();

        return $sale->account->breakdownProducts()
            ->with('product')
            ->orderBy(BreakdownProduct::select('position')
                ->whereColumn('breakdown_product_id', 'breakdown_products.id')
            )
            ->where(fn ($q) => $q->whereNull('max_mileage')->orWhere('max_mileage', '>=', $sale->delivery_mileage))
            ->where(fn ($q) => $q->whereNull('max_age')->orWhere('max_age', '>=', today()->diffInMonths($sale->registration_date)))
            ->when($sale->engine_capacity, fn ($q) => $q->where(fn ($q) => $q->whereNull('max_engine_capacity')->orWhere('max_engine_capacity', '>=', $sale->engine_capacity)))
            ->orderByRaw('max_engine_capacity IS NULL DESC')
            ->orderBy('max_engine_capacity')
            ->orderByRaw('max_mileage IS NULL DESC')
            ->orderBy('max_mileage')
            ->orderByRaw('max_age IS NULL DESC')
            ->orderBy('max_age')
            ->get()
            ->groupBy('breakdown_product_id')
            ->map->first()
            ->values()
            ->mapWithKeys(fn (AccountBreakdownProduct $accountBreakdownProduct) => [$accountBreakdownProduct->getKey() => $accountBreakdownProduct->product->getLabel()]);
    }

    protected function getServicePlanProducts()
    {
        $sale = $this->getSale();

        return $sale->account->servicePlanProducts()
            ->with('product')
            ->when(
                $sale->isPureElectric(),
                fn ($query) => $query->whereHas('product', fn ($q) => $q->where('is_pure_electric', true)),
                fn ($query) => $query->whereHas('product', fn ($q) => $q->where('is_pure_electric', false)),
            )
            ->orderBy(ServicePlanProduct::select('position')
                ->whereColumn('service_plan_product_id', 'service_plan_products.id')
            )
            ->when($sale->engine_capacity, fn ($q) => $q->where(fn ($q) => $q->whereNull('max_engine_capacity')->orWhere('max_engine_capacity', '>=', $sale->engine_capacity)))
            ->orderByRaw('max_engine_capacity IS NULL DESC')
            ->orderBy('max_engine_capacity')
            ->get()
            ->groupBy('service_plan_product_id')
            ->map->first()
            ->values()
            ->mapWithKeys(fn (AccountServicePlanProduct $accountServicePlanProduct) => [$accountServicePlanProduct->getKey() => $accountServicePlanProduct->getLabel()]);
    }

    protected function mutateWarrantyData(): Closure
    {
        return function (VatCalculator $vatCalculator, array $data, Get $get, ManufacturerSurchargeRepository $manufacturerSurchargeRepository): array {
            $accountWarrantyProduct = $get('data.accountWarrantyProduct', true);
            if (! $accountWarrantyProduct) {
                return $data;
            }

            $sale = $this->getSale();
            if ($sale->confirmed_at) {
                throw new Halt;
            }

            $surcharge = $manufacturerSurchargeRepository->lookupSurcharge($this->getSale());

            return [
                'account_id' => $sale->account->id,
                'product_id' => $accountWarrantyProduct->product_id,
                'is_self_funded' => $sale->account->warranty_self_funded,
                'start_date' => $sale->start_date,
                'end_date' => $accountWarrantyProduct->product->is_recurring
                    ? null
                    : $sale->start_date->copy()->addMonths($accountWarrantyProduct->product->period)->subDay(),
                'monthly_selling_price' => $accountWarrantyProduct->monthly_selling_price,
                'monthly_admin_fee' => $accountWarrantyProduct->monthly_admin_fee,
                'monthly_provision' => ($accountWarrantyProduct->monthly_selling_price - $accountWarrantyProduct->monthly_admin_fee) ?: null,
                'admin_fee' => $surcharge->addAdminFeeSurcharge($accountWarrantyProduct->admin_fee),
                'provision' => $surcharge->addProvisionSurcharge($accountWarrantyProduct->provision),
                'vat' => $vatCalculator->getVatAmount($accountWarrantyProduct->admin_fee),
                'selling_price' => $data['selling_price'],
                'sales_vat' => $vatCalculator->getVatAmountIncluded($data['selling_price']),
                'annual_mileage_limit' => $accountWarrantyProduct->annual_mileage_limit,
                'individual_claim_limit' => $accountWarrantyProduct->individual_claim_limit,
                'total_claim_limit' => $accountWarrantyProduct->total_claim_limit,
            ];
        };
    }

    protected function hasBreakdownProduct(): bool
    {
        return isset($this->data['accountWarrantyProduct']->bundledBreakdownProduct) || isset($this->data['accountBreakdownProduct']->id);
    }

    protected function mutateBreakdownPlanData(): Closure
    {
        return function (VatCalculator $vatCalculator, Get $get): array {
            $data = $this->data['breakdownPlan'];

            $sale = $this->getSale();

            /** @var AccountWarrantyBreakdownProductBundle $bundledBreakdownProduct */
            $bundledBreakdownProduct = $get('data.accountWarrantyProduct.bundledBreakdownProduct', true);
            if ($bundledBreakdownProduct) {
                return [
                    'account_id' => $sale->account->id,
                    'breakdown_product_id' => $bundledBreakdownProduct->breakdown_product_id,
                    'is_self_funded' => $sale->account->breakdown_self_funded,
                    'selling_price' => 0,
                    'end_date' => $bundledBreakdownProduct->breakdownProduct->isRecurring()
                        ? null
                        : $sale->start_date->copy()->addMonths($bundledBreakdownProduct->breakdownProduct->period)->subDay(),
                    'admin_fee' => $bundledBreakdownProduct->admin_fee,
                    'vat' => $vatCalculator->getVatAmount($bundledBreakdownProduct->admin_fee),
                    'sales_vat' => 0,
                    'provision' => $bundledBreakdownProduct->provision,
                ];
            }

            /** @var AccountBreakdownProduct $accountBreakdownProduct */
            $accountBreakdownProduct = $get('data.accountBreakdownProduct', true);

            if ($accountBreakdownProduct) {
                return [
                    'account_id' => $sale->account->id,
                    'breakdown_product_id' => $accountBreakdownProduct->breakdown_product_id,
                    'is_self_funded' => $sale->account->breakdown_self_funded,
                    'selling_price' => $data['selling_price'] ?? 0,
                    'end_date' => $accountBreakdownProduct->product->isRecurring()
                        ? null
                        : $sale->start_date->copy()->addMonths($accountBreakdownProduct->product->period)->subDay(),
                    'admin_fee' => $accountBreakdownProduct->admin_fee,
                    'vat' => $vatCalculator->getVatAmount($accountBreakdownProduct->admin_fee),
                    'sales_vat' => $vatCalculator->getVatAmountIncluded($data['selling_price']),
                    'provision' => $accountBreakdownProduct->provision,
                ];
            }

            return [];
        };
    }

    protected function mutateServicePlanData(): Closure
    {
        return function (array $data): array {
            if (! isset($data['account_service_plan_product_id'])) {
                return $data;
            }

            $sale = $this->getSale();
            if ($sale->confirmed_at) {
                throw new Halt;
            }

            $accountServicePlanProduct = AccountServicePlanProduct::where('account_id', $this->getSale()->account_id)
                ->find($data['account_service_plan_product_id']);

            return [
                'account_id' => $sale->account->id,
                'service_plan_product_id' => $accountServicePlanProduct->service_plan_product_id,
                'end_date' => $accountServicePlanProduct->product->is_recurring
                    ? null
                    : $sale->start_date->copy()->addYears($accountServicePlanProduct->duration_years)->subDay(),
                'duration_years' => $accountServicePlanProduct->duration_years,
                'admin_fee' => $accountServicePlanProduct->admin_fee,
                'vat' => 0,
                'selling_price' => $data['selling_price'],
            ];
        };
    }

    protected function getRedirectUrl(): ?string
    {
        return SaleResource::getUrl('view', ['record' => $this->getRecord()]);
    }

    private function getProductGroups(): Collection
    {
        return ProductGroup::query()
            ->withWhereHas(
                'products',
                fn ($q) => $q->withWhereHas('accountProducts', fn ($q) => $q->where('account_id', $this->getSale()->account_id))
            )
            ->get();
    }

    private function mapProductGroups()
    {
        return $this->getProductGroups()
            ->map(fn (ProductGroup $productGroup) => Forms\Components\Grid::make(2)
                ->maxWidth('2xl')
                ->formatStateUsing(function (Forms\Get $get) {
                    return [
                        'productGroups' => $this->getSale()->products()
                            ->with('product')
                            ->get()
                            ->mapWithKeys(fn (SaleProduct $saleProduct) => [
                                $saleProduct->product->product_group_id => $saleProduct->only('product_id', 'selling_price'),
                            ])
                            ->toArray(),
                    ];
                })
                ->schema([
                    Forms\Components\Select::make("productGroups.{$productGroup->id}.product_id")
                        ->label($productGroup->name)
                        ->live()
                        ->placeholder("No {$productGroup->name}")
                        ->live()
                        ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) use ($productGroup) {
                            $productId = $get("productGroups.{$productGroup->id}.product_id");
                            if ($productId) {
                                $sellingPrice = $productGroup->products->find($productId)?->accountProducts->first()->selling_price;
                            } else {
                                $sellingPrice = null;
                            }

                            $set("productGroups.{$productGroup->id}.selling_price", $sellingPrice);

                            $this->onSellingPriceUpdate($set);
                        })
                        ->options($productGroup->products->pluck('name', 'id')),
                    Forms\Components\TextInput::make("productGroups.{$productGroup->id}.selling_price")
                        ->lazy()
                        ->prefix('£')
                        ->visible(fn (Forms\Get $get): bool => (bool) $get("productGroups.{$productGroup->id}.product_id"))
                        ->numeric()
                        ->required(),
                ]));
    }

    protected function getSale(): Sale
    {
        return $this->getRecord();
    }
}
