<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Filament\Resources\CustomerResource;
use App\Filament\Resources\SaleResource;
use App\Filament\Widgets\MissingPaymentMethodAlert;
use App\Models\Sale;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;

class ViewSale extends ViewRecord
{
    protected static string $resource = SaleResource::class;

    protected static ?string $title = 'Sale Information';

    public function mount(int|string $record): void
    {
        parent::mount($record);
        if (! $this->getRecord()->confirmed_at) {
            $this->redirect(AddSaleProducts::getUrl(['record' => $this->getRecord()]));
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            EditAction::make()->outlined(),
            Action::make('resend-documents')
                ->visible(fn (Sale $sale) => $sale->documentsCanBeGenerated())
                ->outlined()
                ->label('Resend Documents by email')
                ->icon('heroicon-o-envelope')
                ->requiresConfirmation()
                ->action(fn (Sale $sale) => $sale->sendWelcomeAndDocumentNotification()),
            Action::make('print-sale-documents')
                ->visible(fn (Sale $sale) => $sale->documentsCanBeGenerated())
                ->label('Print')
                ->icon('heroicon-o-printer')
                ->url(fn (Sale $sale) => route('documents.pdf', $sale))
                ->openUrlInNewTab(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            MissingPaymentMethodAlert::make(),
        ];
    }

//    public function infolist(Infolist $infolist): Infolist
//    {
//        return $infolist
//            ->schema(fn (Sale $record) => [
//                CustomerResource::getCustomerDetailsSection($record->customer)->relationship('customer'),
//                SaleResource::getVehicleDetailsInfolistSection(),
//                SaleResource::getSaleDetailsInfolistSection(),
//            ]);
//    }
}
