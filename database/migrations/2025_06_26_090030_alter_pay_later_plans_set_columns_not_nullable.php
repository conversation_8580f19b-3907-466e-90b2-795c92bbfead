<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('UPDATE pay_later_plans SET commission_rate = 0 WHERE commission_rate IS NULL');
        DB::statement('UPDATE pay_later_plans SET commission_rate_margin = 0 WHERE commission_rate_margin IS NULL');
        DB::statement('UPDATE pay_later_plans SET min_amount = 0 WHERE min_amount IS NULL');

        Schema::table('pay_later_plans', function (Blueprint $table) {
            $table->decimal('commission_rate', 4, 2)->nullable(false)->change();
            $table->decimal('commission_rate_margin', 4, 2)->nullable(false)->default(0)->change();
            $table->unsignedInteger('min_amount')->nullable(false)->default(0)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pay_later_plans', function (Blueprint $table) {
            $table->decimal('commission_rate', 4, 2)->nullable()->change();
            $table->decimal('commission_rate_margin', 4, 2)->nullable()->change();
            $table->unsignedInteger('min_amount')->nullable()->change();
        });
    }
};
