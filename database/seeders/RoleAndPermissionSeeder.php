<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleAndPermissionSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->setupPermissionsAndRoles();
    }

    private function setupPermissionsAndRoles()
    {
        $roles = [
            'Admin' => [
                'is_internal' => true,
                'permissions' => [
                    'dashboard.view',
                    'accounts.manage',
                    'dealerships.view',
                    'dealerships.create',
                    'dealerships.update',
                    'dealerships.delete',
                    'dealerships.restore',
                    'sales-people.manage',
                    'repairers.view',
                    'repairers.manage',
                    'products.manage',
                    'customers.create',
                    'customers.view',
                    'customers.update',
                    'pay-later-services.create',
                    'pay-later-services.view',
                    'sales.view',
                    'sales.create',
                    'sales.update',
                    'claims.view',
                    'claims.update',
                    'claims.create',
                    'claims.delete',
                    'claims.uses-browser-plugin',
                    'claim-estimates.view',
                    'claim-estimates.create',
                    'claim-estimates.update',
                    'claim-estimates.delete',
                    'claim-authorisations.view',
                    'claim-authorisations.create',
                    'claim-authorisations.update',
                    'claim-rejections.create',
                    'breakdown-claims.view',
                    'breakdown-claims.create',
                    'breakdown-claims.update',
                    'service-plan-redemptions.view',
                    'service-plan-redemptions.create',
                    'service-plan-redemptions.update',
                    'invoices.view',
                    'invoices.create',
                    'payments.view',
                    'users.manage',
                    'warranties.export',
                    'breakdownPlans.export',
                    'claims.export',
                    'component-list.view',
                ],
            ],
            'Owner' => [
                'is_internal' => true,
                'permissions' => [
                    'admin-users.manage',
                    'users.impersonate',
                    'accounts.update-funding-types',
                    'sales-leads.call',
                    'sales-leads.view',
                    'payments.update',
                    'pay-later-agreements.view',
                ],
            ],
            'Developer' => [
                'is_internal' => true,
                'permissions' => [
                    'admin-users.manage',
                    'users.impersonate',
                    'accounts.update-funding-types',
                    'sales-leads.call',
                    'sales-leads.view',
                    'pay-later-agreements.view',
                ],
            ],
            'CRM Sales' => [
                'is_internal' => true,
                'permissions' => [
                    'sales-leads.call',
                    'sales-leads.view',
                ],
            ],
            'Dealership Manager' => [
                'is_internal' => false,
                'permissions' => [
                    'dashboard.view',
                    'dealerships.view',
                    'dealerships.create',
                    'dealerships.update',
                    'dealerships.delete',
                    'dealerships.restore',
                    'sales-people.manage',
                    'repairers.view',
                    'products.manage',
                    'pay-later-services.create',
                    'pay-later-services.view',
                    'customers.create',
                    'customers.view',
                    'customers.update',
                    'sales.view',
                    'sales.create',
                    'claims.view',
                    'claims.create',
                    'claim-estimates.view',
                    'claim-estimates.create',
                    'claim-authorisations.view',
                    'claim-authorisations.create',
                    'claim-rejections.create',
                    'breakdown-claims.view',
                    'service-plan-redemptions.view',
                    'invoices.view',
                    'warranties.export',
                    'breakdownPlans.export',
                ],
            ],
            'Dealership Sales' => [
                'is_internal' => false,
                'permissions' => [
                    'pay-later-services.create',
                    'pay-later-services.view',
                    'customers.create',
                    'customers.view',
                    'customers.update',
                    'sales.view',
                    'sales.create',
                    'repairers.view',
                    'claims.view',
                    'claim-estimates.view',
                ],
            ],
        ];

        $allPermissions = collect($roles)->pluck('permissions')->flatten()->unique()->values();
        // Create permissions that don't exist
        Permission::upsert(
            $allPermissions->map(fn ($permissionName) => ['name' => $permissionName, 'guard_name' => 'web'])->toArray(),
            ['name', 'guard_name']
        );
        // Delete removed permissions
        Permission::whereNotIn('name', $allPermissions)->delete();

        // Seed roles and permissions
        foreach ($roles as $roleName => $config) {
            Role::updateOrCreate([
                'name' => $roleName,
            ], [
                'is_internal' => $config['is_internal'],
            ])->syncPermissions($config['permissions']);
        }

        if (! app()->isProduction()) {
            $this->syncUserRoles();
        }
    }

    private function syncUserRoles(): void
    {
        User::query()
            ->whereIn('email', [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ])->each(function (User $user) {
                $user->syncRoles('Admin', 'Owner', 'Developer');
                $user->update(['is_internal' => true]);
            });
    }
}
