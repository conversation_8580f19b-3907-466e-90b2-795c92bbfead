<?php

use App\Enums\PaymentProvider;
use App\Models\Sale;
use App\Services\Payments\PayLater\ApplicationRedirectData;
use App\Services\Payments\PayLater\ApplicationStatusData;
use App\Services\Payments\PayLater\PaymentAssistProcessor;
use App\Services\Payments\PayLater\PreApprovalData;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

beforeEach(function () {
    Http::preventStrayRequests();

    $this->sale = Sale::factory()->withPayLaterAgreementSelected()->create();
    $this->processor = app(PaymentAssistProcessor::class);
});

test('get processor identifier', function () {
    expect($this->processor->getProcessorIdentifier())->toBe(PaymentProvider::PAYMENT_ASSIST);
});

test('get plan breakdown', function () {

    Http::fake(
        Http::response([
            'msg' => null,
            'data' => [
                'plan' => '4-Payment', /* the plan name */
                'amount' => 50000,
                'interest' => 0, /* the total interest (in pence) */
                'repayable' => 50000, /* the total amount repayable (in pence) */
                'schedule' => [
                    [
                        'date' => '2019-03-12',
                        'amount' => 12500,
                    ],
                    [
                        'date' => '2019-04-12',
                        'amount' => 12500,
                    ],
                    [
                        'date' => '2019-05-12',
                        'amount' => 12500,
                    ],
                    [
                        'date' => '2019-06-12',
                        'amount' => 12500,
                    ],
                ],
            ],
        ], 200),
    );

    $result = $this->processor->setCredentials(
        apiKey: 'some-api-key',
        apiSecret: 'some-api-secret',
        isLive: false,
    )->getPlanBreakdown($this->sale->payLaterAgreement->payLaterPlan, $this->sale->payLaterAgreement->amount);

    Http::assertSent(fn (Request $request) => $request->toPsrRequest()->getUri()->getPath() === '/plan' &&
        $request->method() === 'POST' &&
        $request['plan_id'] === $this->sale->payLaterAgreement->payLaterPlan->provider_plan_id &&
        $request['amount'] == $this->sale->payLaterAgreement->amount * 100);

    expect($result)->toBeInstanceOf(\App\Services\Payments\PayLater\PlanBreakdownData::class)
        ->name->toBe('4-Payment')
        ->amount->toEqual(500)
        ->interest->toEqual(0)
        ->repayable->toEqual(500)
        ->and(count($result->schedule))->toBe(4)
        ->and($result->schedule[0])
        ->date->toBe('2019-03-12')
        ->amount->toEqual(125);
});

test('pre approve successful', function () {
    $apiUrl = PaymentAssistProcessor::STAGING_API_ENDPOINT.'/preapproval';

    Http::fake([
        $apiUrl => Http::response([
            'status' => 'ok',
            'msg' => null,
            'data' => [
                'approved' => true,
            ],
        ], 200),
    ]);

    $result = $this->processor->setCredentials(
        apiKey: 'some-api-key',
        apiSecret: 'some-api-secret',
        isLive: false,
    )->preApprove($this->sale->customer);

    Http::assertSent(function (Request $request) use ($apiUrl) {
        return $request->url() === $apiUrl &&
            $request->method() === 'POST' &&
            $request['api_key'] === 'some-api-key' &&
            $request['f_name'] === $this->sale->customer->first_name &&
            $request['s_name'] === $this->sale->customer->last_name &&
            $request['addr1'] === $this->sale->customer->address_1;
    });

    expect($result)->toBeInstanceOf(PreApprovalData::class)
        ->and($result->approved)->toBeTrue()
        ->and($result->message)->toBeNull();
});

test('pre approve declined', function () {
    $apiUrl = PaymentAssistProcessor::STAGING_API_ENDPOINT.'/preapproval';

    Http::fake([
        $apiUrl => Http::response([
            'status' => 'ok',
            'msg' => 'some message',
            'data' => [
                'approved' => false,
            ],
        ], 200),
    ]);

    $result = $this->processor->setCredentials(
        apiKey: 'some-api-key',
        apiSecret: 'some-api-secret',
        isLive: false,
    )->preApprove($this->sale->customer);

    expect($result)->toBeInstanceOf(PreApprovalData::class)
        ->and($result->approved)->toBeFalse()
        ->and($result->message)->toBe('some message');
});

test('start application', function () {
    $apiUrl = PaymentAssistProcessor::STAGING_API_ENDPOINT.'/begin';

    $token = Str::uuid()->toString();

    Http::fake([
        $apiUrl => Http::response([
            'status' => 'ok',
            'signature' => 'signature',
            'msg' => null,
            'data' => [
                'token' => $token,
                'url' => 'https://some-redirect-url.com',
            ],
        ], 200),
    ]);

    $reference = 'ref-123456';

    $result = $this->processor->setCredentials(
        apiKey: 'some-api-key',
        apiSecret: 'some-api-secret',
        isLive: false,
    )->startApplication(
        payLaterAgreement: $this->sale->payLaterAgreement,
        reference: $reference,
    );

    Http::assertSent(function (Request $request) use ($apiUrl, $reference) {
        return $request->url() === $apiUrl &&
            $request->method() === 'POST' &&
            $request['api_key'] === 'some-api-key' &&
            $request['order_id'] === $reference &&
            $request['plan_id'] === $this->sale->payLaterAgreement->payLaterPlan->provider_plan_id &&
            $request['amount'] == $this->sale->payLaterAgreement->loan_amount * 100 &&
            $request['f_name'] === $this->sale->customer->first_name &&
            $request['s_name'] === $this->sale->customer->last_name &&
            $request['addr1'] === $this->sale->customer->address_1 &&
            $request['addr2'] === $this->sale->customer->address_2 &&
            $request['town'] === $this->sale->customer->city &&
            $request['postcode'] === $this->sale->customer->postcode &&
            $request['email'] === $this->sale->customer->email &&
            $request['telephone'] === $this->sale->customer->phone &&
            $request['reg_no'] === $this->sale->vrm &&
            $request['description'] === $this->sale->description() &&
            $request['webhook_url'] === route('webhooks.payment-assist');
    });

    expect($result)->toBeInstanceOf(ApplicationRedirectData::class)
        ->and($result->token)->toBe($token)
        ->and($result->url)->toBe('https://some-redirect-url.com');
});

test('get application status', function () {
    $token = Str::uuid()->toString();

    Http::fake(
        Http::response([
            'msg' => null,
            'data' => [
                'token' => $token,
                'status' => 'pending',
                'amount' => 23456,
                'plan_id' => '<SOME_PLAN_ID>',
                'pa_ref' => '<PAYMENT_ASSIST_REFERENCE>',
                'requires_invoice' => true,
                'has_invoice' => false,
                'last_accessed_at' => '2021-01-01 12:00:00',
            ],
        ], 200),
    );

    $result = $this->processor->setCredentials(
        apiKey: 'some-api-key',
        apiSecret: 'some-api-secret',
        isLive: false,
    )->getApplicationStatus($token);

    Http::assertSent(fn (Request $request) => $request->toPsrRequest()->getUri()->getPath() === '/status' &&
        $request->method() === 'GET' &&
        $request['api_key'] === 'some-api-key' &&
        $request['token'] === $token
    );

    expect($result)->toBeInstanceOf(ApplicationStatusData::class)
        ->and($result->token)->toBe($token)
        ->and($result->providerReference)->toBe('<PAYMENT_ASSIST_REFERENCE>')
        ->and($result->status)->toBe('pending')
        ->and($result->planId)->toBe('<SOME_PLAN_ID>')
        ->and($result->requiresInvoice)->toBeTrue()
        ->and($result->hasInvoice)->toBeFalse()
        ->and($result->lastAccessedAt)->toEqual('2021-01-01 12:00:00');
});

test('upload invoice successful', function () {
    Http::fake([
        'api.staging.payment-assist.co.uk/v1/application/app-123456/document' => Http::response([
            'success' => true,
        ], 200),
    ]);

    $result = $this->processor->setCredentials(
        apiKey: 'some-api-key',
        apiSecret: 'some-api-secret',
        isLive: false,
    )->uploadInvoice('app-123456', $this->invoice);

    Http::assertSent(function (Request $request) {
        return $request->url() == 'https://api.staging.payment-assist.co.uk/v1/application/app-123456/document' &&
            $request->method() === 'POST' &&
            $request['api_key'] === 'some-api-key' &&
            $request->hasHeader('Authorization');
    });

    expect($result)->toBeTrue();
})->todo();

test('upload invoice failure', function () {
    Http::fake([
        'api.staging.payment-assist.co.uk/v1/application/app-123456/document' => Http::response([
            'error' => 'Invalid document',
        ], 400),
    ]);

    $result = $this->processor->setCredentials(
        apiKey: 'some-api-key',
        apiSecret: 'some-api-secret',
        isLive: false,
    )->uploadInvoice('app-123456', $this->invoice);

    expect($result)->toBeFalse();
})->todo();
